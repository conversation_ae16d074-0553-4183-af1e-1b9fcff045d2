import Image from 'next/image'
import Link from 'next/link'
import { ThemeController } from '@/components/ThemeController'

export default function LoginPage() {
  return (
    <div className="grid grid-cols-12 overflow-auto h-screen">
      <div className="relative hidden col-span-12 md:col-span-6 lg:col-span-7 md:block">
        <Image src="/placeholder.svg" alt="placeholder" fill priority />
      </div>
      <div className="col-span-12 md:col-span-6 lg:col-span-5">
        <div className="flex flex-col items-stretch md:p-8 p-6 lg:p-16">
          <div className="flex items-center justify-between relative">
            <Link href="/">
              <Image src="/logo-light.svg" alt="logo" width={103} height={20} />
            </Link>
            <ThemeController />
          </div>
          <h3 className="mt-8 text-center text-xl font-semibold md:mt-12 lg:mt-24">
            Welcome back!
          </h3>
          <h3 className="mt-2 text-center text-sm text-base-content/70">
            Enter your credentials to log in to the application
          </h3>
          <div className="mt-6 md:mt-10">
            <fieldset className="fieldset">
              <legend className="fieldset-legend text-sm">Email Address</legend>
              <label className="input w-full focus:outline-0">
                <span className="icon-[solar--mailbox-bold-duotone]"></span>
                <input type="email" placeholder="Email Address" className="grow focus:outline-0" />
              </label>
            </fieldset>
            <fieldset className="fieldset">
              <legend className="fieldset-legend text-sm">Password</legend>
              <label className="input w-full focus:outline-0">
                <span className="icon-[solar--key-bold-duotone]"></span>
                <input type="password" placeholder="Password" className="grow focus:outline-0" />
                <button className="btn btn-xs btn-ghost btn-circle" aria-label="Password">
                  <span className="icon-[solar--key-bold-duotone]"></span>
                </button>
              </label>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  )
}
